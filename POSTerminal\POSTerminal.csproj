﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{8E4DCD5A-8F89-483C-9011-338B70CFE0F9}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>POSTerminal</RootNamespace>
    <AssemblyName>POSTerminal</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Configuration.ConfigurationManager, Version=8.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Configuration.ConfigurationManager.8.0.0\lib\net462\System.Configuration.ConfigurationManager.dll</HintPath>
    </Reference>
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="CBObject.cs" />
    <Compile Include="CountDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="CountDialog.Designer.cs">
      <DependentUpon>CountDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Database\DALObject.cs" />
    <Compile Include="Database\DALObjectException.cs" />
    <Compile Include="Database\DALObjectType.cs" />
    <Compile Include="Database\DBConnection.cs" />
    <Compile Include="Database\DBManager.cs" />
    <Compile Include="Effects.cs" />
    <Compile Include="FiskalnaIntegracija.cs" />
    <Compile Include="FrmGlavna.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmGlavna.Designer.cs">
      <DependentUpon>FrmGlavna.cs</DependentUpon>
    </Compile>
    <Compile Include="FrmPrijava.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmPrijava.Designer.cs">
      <DependentUpon>FrmPrijava.cs</DependentUpon>
    </Compile>
    <Compile Include="ManagerDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ManagerDialog.Designer.cs">
      <DependentUpon>ManagerDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="Model\IModelObject.cs" />
    <Compile Include="Model\Mesto.cs" />
    <Compile Include="Model\NacinPlacanja.cs" />
    <Compile Include="Model\Opstina.cs" />
    <Compile Include="Model\PoreskaGrupa.cs" />
    <Compile Include="Model\Preduzece.cs" />
    <Compile Include="Model\ProdajnoMesto.cs" />
    <Compile Include="Model\Proizvod.cs" />
    <Compile Include="Model\Racun.cs" />
    <Compile Include="Model\StatusZaposlenog.cs" />
    <Compile Include="Model\StavkaRacuna.cs" />
    <Compile Include="Model\Zaposleni.cs" />
    <Compile Include="PaymentDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PaymentDialog.Designer.cs">
      <DependentUpon>PaymentDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="PrintPreview.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PrintPreview.Designer.cs">
      <DependentUpon>PrintPreview.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="SimuScan.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SimuScan.Designer.cs">
      <DependentUpon>SimuScan.cs</DependentUpon>
    </Compile>
    <EmbeddedResource Include="CountDialog.resx">
      <DependentUpon>CountDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmGlavna.resx">
      <DependentUpon>FrmGlavna.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmPrijava.resx">
      <DependentUpon>FrmPrijava.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ManagerDialog.resx">
      <DependentUpon>ManagerDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PaymentDialog.resx">
      <DependentUpon>PaymentDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PrintPreview.resx">
      <DependentUpon>PrintPreview.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <EmbeddedResource Include="SimuScan.resx">
      <DependentUpon>SimuScan.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Logo.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\barcode.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\486.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\filter_remove.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Logo.ico" />
    <Content Include="Sounds\PRINTER.wav">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\RacunCyr.txt">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\RacunLat.txt">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <None Include="Resources\credit_card.png" />
    <None Include="Resources\100din.jpg" />
    <Content Include="Sounds\BUTTON.wav">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Sounds\SCANBEEP.wav">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>