﻿using POSTerminal.Model;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace POSTerminal
{
    public partial class PaymentDialog : Form
    {
        public NacinPlacanja nacinplacanja;
        public PaymentDialog()
        {
            InitializeComponent();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
            nacinplacanja = NacinPlacanja.Kes;
            this.Close();
        }

        private void btkartica_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
            nacinplacanja = NacinPlacanja.Kartica;
            this.Close();
        }

        private void PaymentDialog_Load(object sender, EventArgs e)
        {

        }
    }
}
