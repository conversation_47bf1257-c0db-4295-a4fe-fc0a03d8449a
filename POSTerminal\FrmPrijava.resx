﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAHCAAAAEAIACoDgAAFgAAACgAAAAcAAAAQAAAAAEAIAAAAAAAAA4AACMuAAAjLgAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD7l9gBB2v4AQdz+Fj/V+ZUwp8vHK4+wQzWR
        rwIvkrIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAEDb+gBE4uwAP9r9NEDZ/rY81Pv8LaTH/ymRsuUqkrN2LZGyESqS
        swA9ka8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAOe/3AD7Z/AA/2vsJPtj9ZT/Z/uBC3P//PdT6/yyixf8okbL/KZKz+yqSs7krkrM6M5KwAi+S
        sgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQNz4ADzX
        /wA92fwhPtj+nT/a/vdD3P//Rd3//z7T+f8rocX/J5Cy/yiRs/8pkrP/KpKz5yuSs3gtkbERKpKzAEKN
        qAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPdn8AD7a+wM72P5KPdn+zUDb
        //9D3P//Rd7//0be//8/0vf/KqDD/yaQsv8nkbP/KJGz/ymSs/8pkrP7KpKzuC2Rsjo2j64CMJCwAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABN4ukAPNf+AD7Y/BI92P6AQtb77UHa/v9D3f//RN3//0be
        //9E3P//OdP5/ySnzv8lkrT/JpGy/yaRs/8nkbP/KZGz/ymSs/8rkbLmLJGydy6RsRErkbIAPZCuAAAA
        AAAAAAAAAAAAAAAAAABA2foAWOrjADzX/TI82P62S9T2/He4y/9G0PL/RN7//0Pd//9A1/z/RLrZ/zGQ
        rP8PmMH/GKPO/yOZvf8mkbP/JpGy/yeRsv8okbL/KZGy/yqRsvsrkbK3LZGyODeRrwExkbEAAAAAAAAA
        AAAAAAAARtn8Cj7X/WI91/7fPdr//13O6f9xr8P/P9T3/0Db//8/ye3/VKC2/3WHjP9MiJv/I26I/xZ4
        mP8Qlr//G5/H/ySVuP8mkLL/J5Cy/yiRsv8pkbL/KpGy/yyRsuUtkbJ0L5GxEC2RsgBDkKkAAAAAAETY
        /XdA2P72QNr+/0Hb//9G2Pr/RNP4/z3T+v9ItdX/aoyY/3mMkf9jtMb/OLbX/yCLrf8meZX/ImiB/xN7
        m/8SmcH/HpzC/yWTtf8mkLH/J5Cy/ymRsv8qkbL/LJCy+y2RsrQvkLE2OJKvATKRsABC1/6pQNn+/0Hb
        /v9A2///Pdf+/0DC5v9cmq3/d4iN/2qksv9Pzur/Qd///zPO8f8jn8P/H46x/yKIqf8mc43/H2mC/xKC
        pf8UmsP/IJi9/yaRs/8nkLH/KZCx/yqQsv8skLL/LZCy4y+QsnA1kbARQNf+wz7a/v892f//PMzy/06n
        wP9viJD/cJeg/1XC2/9C3Pz/N93//zDa/v8w2///L8/z/yWlyP8fj7H/II6w/yOEpP8mb4j/HG2I/xGJ
        rv8XmsL/IpW5/yaQsf8nkLH/KZCx/yuPsf8skLH6MpCwiT7X/ts50/v/Q7bW/2OOm/9zjJP/XLTI/0TW
        9v853f//MNr//y3Z//8u2v//Ltr//y3a//8v0/j/J6vP/x+Psv8gjrD/II2v/ySAn/8kbIX/GHGO/xGO
        tf8amcD/I5K1/yWPsP8mj7D/J4+w/y2PsLVCyOzrVp2z/3CHjv9jpbT/R87s/znc//8x2v//Ldn//y3Z
        //8t2f7/KtP9/yWs+f8fgu3/I7r1/zDW+/8ps9f/H5G0/x+Or/8fjrD/IIyt/yR8mf8iaoP/FXiX/xKT
        uv8cl73/I5Cy/yOOr/8oj6/NbpCauGmXov9Nwt3/O9r9/zLb//8t2f7/LNn+/y3Y//8q1v7/J8P8/yVv
        8v8pOvD/KTrr/x2K0v8gyuX/Ltb9/yu63/8flLb/Ho6v/x+OsP8fjrD/IImr/yR4lP8ga4X/FH6g/xWV
        vf8flbn/Jo+w40zD37091Pb/M9v//y7Z/v8t2f7/K9n//ynV/P8lz+b/IM3B/xu52f8pcPX/VnHs/2ep
        2/8v0aX/LsWe/zu70v8v1fz/LcHm/yCYu/8djrD/Ho6w/x6Nr/8fja//IIen/yN0kP8cb4v/IY2y/TWl
        y9M52/+oMNr/+y3Z/v8s2P7/K9X9/0TB3P81xbn/J9uc/y7am/8/tqr/QqzP/3fU8f/C4er/fM3V/5an
        hP/Rikb/aaem/zDR+P8vx+3/IZ3A/xyOr/8djrD/Ho2v/x6Nr/8fjK//IYSk/yt5le4/k7JOPNr+EzTZ
        /ncv2v/nPszq/4aik//EjVT/oaBs/13Pvf+b1tf/kLXE/26lqv8ov8H/TtDq/73i8P+vyND/yqBy/+aJ
        O/9zpaH/MdX+/zDM9P8iosf/HI6w/x2Or/8djq//Ho2v/x+Nr/8gjK79Joysc0Tb/QBT3PwCM9z/PEvP
        5buirIr8sa+b/7TK0P+/2ef/rtfp/6/W5/+D0ND/M9eg/yDOpf85zN3/o97w/6zV4f9/uLf/U77U/yu3
        4v8kpMj/IcDr/ySoz/8cj7H/HY6w/x2OsP8ejq//H42v/yaPsI4AAAAAWN78ACTg/wAu4P8TNtn7fnbg
        +Oqo3O3/r9Xm/7za5/+z1+b/s9jo/57X1/9G2K//JtDB/zrT+f9AzfX/KKLI/yt0jP83Ulv/NkFG/yFr
        gv8luuX/JKrS/xyJqf8dh6f/HYem/x6Gpf8khaOmAAAAAAAAAAAAAAAAN9z/ADTe/wIu2f9BVdz7wZjg
        8f2y1+b/utnm/6fa6/9/2/X/RNX3/y/V/P8v0///MLnj/z5kcf9dYGH/hIqO/2Fmaf85Ozz/JVZm/y1q
        e/8tRk3/Jz1E/yg9RP8pPUP/LT1BxAAAAAAAAAAAAAAAAAAAAABX2/0AJNj/ACrZ/xY/2v2EeeD37WLZ
        9/891/3/Ltb//y/W//8v1P//L9T//yvO/v9ZwOD/wMvS/9rh5v+boqb/Y2hr/0hIR/9bWVn/R0ZG/yop
        KP8rKir/LCoq/y4tLdoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAp2P8AKNj/Ay/Y/0Yw2P/GMNf//jDX
        //8u1///K9H8/y7A6P82ocH/dqWz/+Pl5v/g4OD/sbO0/29ydP9mZmb/X19f/05OTv8vLy//Kioq/ysr
        K/8uLi7jAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAF3c+wAu1/8APNn+GTXX/osyxerzN6bE/0iI
        mv9ddHr/bW9w/3p4eP+Kior/dXV1/19fX/9RUVH/SUlJ/0ZGRv9ISEj/QEBA/y4uLv8rKyv/Ly8vsQAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEq41ABAyu0GXnV8qG1oZf+GaFX/pndI/41z
        UP9WV1f/TExM/0VFRf8/Pz//Ozs7/zo6Ov9BQUH/TExM+1NTU+VNTU29NjY2gjMzMyYAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACKZ10AZWZmAGhpallxVUn4qVgp/8JuJf+vdC3/Ukk+/zg5
        Of8wMDD/NDQ0/UFBQepKSkrEVVVVj15eXlZnZ2cngYGBCv///wDW1tYAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAFJSUgBPVVgMTUZDoHFJNP56Uzj/Z1VC/lBNSO1ISEnPV1hZ8kdH
        R6pfX18qcXFxDLu7uwGcnJwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAACZl5cARUlLAEdKTBRMTE1uUVNUkVVYWmRZXF8uc32HF5CUl79oa2yPLCwsIzIy
        Mgn///8AWFhYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAFRSTQBWVlMbZlxOX3djS5iGblHrdWFK+C8uLugoKCi3Ly8vJy0t
        LQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAABnUz0AY1dJap9wQP+5ekD/wHk//5dnRP8vLi7/JiYm+C0tLUcqKioAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAZ0s4AGJRRWyfYTr/qmQ//55gQ/9/Wkr/MTAw/yUlJfwsLCxRKCgoAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAF9V
        UQBfWFRkb1lR/21dV/9lXVv/X11c/0FBQf8tLS37Ly8vWSoqKgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABcXV0AX19gL1dZ
        Wr9ZWlvqW1tc311dXbpeXl6RUVFRZ0JCQh5AQEAAAAAAAAAAAAAAAAAAAAAAAP/B//D/gP/w/gA/8PwA
        H/DwAAfw4AAD8MAAAPAAAABwAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgAAAAOAA
        AADwAAAA/AAAAP4AAAD/gAAA/8AAAP/gADD/4AHw//AD8P/+AfD//gHw//4B8P/+AfD//gHw
</value>
  </data>
</root>